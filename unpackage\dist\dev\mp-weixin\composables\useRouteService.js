"use strict";const P=require("../libs/amap-wx.130.js"),_=require("../libs/config.js"),r=require("../utils/mapUtils.js");function W(){function k(){return new P.AMapWX({key:_.gaode_key.config.key})}function M(m,s,e){const{origin:n,destination:g}=m;if(!n||!g)return;k().getDrivingRoute({origin:n,destination:g,success:function(t){var o,f,h,u;const p=r.parseRoutePoints(((f=(o=t.paths)==null?void 0:o[0])==null?void 0:f.steps)||[]),c=r.createPolyline(p),i={distance:(u=(h=t.paths)==null?void 0:h[0])!=null&&u.distance?r.formatDistance(t.paths[0].distance):"",cost:t.taxi_cost?r.formatTaxiCost(t.taxi_cost):"",polyline:c};s==null||s(i)},fail:function(t){r.handleMapError("驾车路线规划",t),e==null||e(t)}})}function b(m,s,e){const{origin:n,destination:g}=m;if(!n||!g)return;k().getWalkingRoute({origin:n,destination:g,success:function(t){var o,f,h,u,d,R;const p=r.parseRoutePoints(((f=(o=t.paths)==null?void 0:o[0])==null?void 0:f.steps)||[]),c=r.createPolyline(p),i={distance:(u=(h=t.paths)==null?void 0:h[0])!=null&&u.distance?r.formatDistance(t.paths[0].distance):"",cost:(R=(d=t.paths)==null?void 0:d[0])!=null&&R.duration?r.formatDuration(t.paths[0].duration):"",polyline:c};s==null||s(i)},fail:function(t){r.handleMapError("步行路线规划",t),e==null||e(t)}})}function w(m,s,e){const{origin:n,destination:g,city:a="广州",cityd:t}=m;if(!n||!g)return;const p=k();p.getWalkingRoute({origin:n,destination:g,success:function(c){var f,h;const i=Number((h=(f=c.paths)==null?void 0:f[0])==null?void 0:h.duration)||0,o={origin:n,destination:g,city:a};t&&t!==a&&Object.assign(o,{cityd:t}),p.getTransitRoute({...o,success:function(u){var d,R;if(u!=null&&u.transits&&u.transits.length>0){const l=u.transits[0],y=Number(l.duration)||0;if(console.log("🚶‍♂️ 步行时间:",i,"秒 (",Math.round(i/60),"分钟)",typeof i),console.log("🚌 公交时间:",y,"秒 (",Math.round(y/60),"分钟)",typeof y),console.log("🔍 原始数据:",{walkingRaw:(R=(d=c.paths)==null?void 0:d[0])==null?void 0:R.duration,transitRaw:l.duration}),console.log("🔍 条件判断:",{"transitDuration > walkingDuration":y>i,"walkingDuration > 0":i>0,最终判断:y>i&&i>0}),y>i&&i>0)console.log("✅ 触发步行建议"),e==null||e({message:"步行更加便利",walkingTime:Math.round(i/60),transitTime:Math.round(y/60),showWalkingSuggestion:!0});else{console.log("❌ 显示公交路线");const D=r.processTransitData(u.transits);s==null||s({transits:D})}}else{const l=t&&t!==a?`未找到从${a}到${t}的公交路线，建议选择其他交通方式`:"未找到公交路线";e==null||e({message:l})}},fail:function(u){r.handleMapError("公交路线规划",u),e==null||e(u)}})},fail:function(c){console.warn("步行路线获取失败，继续获取公交路线:",c);const i={origin:n,destination:g,city:a};t&&t!==a&&Object.assign(i,{cityd:t}),p.getTransitRoute({...i,success:function(o){if(o!=null&&o.transits){const f=r.processTransitData(o.transits);s==null||s({transits:f})}else{const f=t&&t!==a?`未找到从${a}到${t}的公交路线，建议选择其他交通方式`:"未找到公交路线";e==null||e({message:f})}},fail:function(o){r.handleMapError("公交路线规划",o),e==null||e(o)}})}})}function T(m,s,e){const{origin:n,destination:g}=m;if(!n||!g)return;k().getRidingRoute({origin:n,destination:g,success:function(t){var p;if((p=t==null?void 0:t.paths)!=null&&p[0]){const c=t.paths[0],i=r.parseRidingPoints(c.rides||[]),o=r.createPolyline(i),f={distance:c.distance?r.formatDistance(c.distance):"",cost:c.duration?r.formatDuration(c.duration):"",polyline:o};s==null||s(f)}else e==null||e({message:"未找到骑行路线"})},fail:function(t){r.handleMapError("骑行路线规划",t),e==null||e(t)}})}function A(m,s,e,n){switch(m){case"car":M(s,e,n);break;case"walk":b(s,e,n);break;case"bus":w(s,e,n);break;case"riding":T(s,e,n);break;default:n==null||n({message:"不支持的路线类型"})}}return{getDrivingRoute:M,getWalkingRoute:b,getTransitRoute:w,getRidingRoute:T,planRoute:A}}exports.useRouteService=W;
