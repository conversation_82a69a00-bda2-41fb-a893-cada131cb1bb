/**
 * 实时定位服务组合式函数
 * 提供高效的实时位置跟踪功能，智能控制API调用频率
 */

import { ref, computed, onUnmounted } from 'vue'
import { handleMapError } from '../utils/mapUtils.js'

export function useRealTimeLocation() {
  
  // ===== 响应式状态 =====
  
  // 实时定位开关状态
  const isTracking = ref(false)
  
  // 当前实时位置信息
  const currentPosition = ref({
    longitude: 0,
    latitude: 0,
    accuracy: 0,
    timestamp: 0
  })
  
  // 移动状态检测
  const isMoving = ref(false)

  // 位置历史记录（用于移动检测）
  const positionHistory = ref([])

  // ===== 配置参数 =====

  // 移动检测的距离阈值（米）
  const MOVEMENT_THRESHOLD = 10

  // 位置历史记录的最大长度
  const MAX_HISTORY_LENGTH = 5
  
  // ===== 计算属性 =====

  /**
   * 是否有有效位置
   */
  const hasValidPosition = computed(() => {
    return currentPosition.value.longitude !== 0 && currentPosition.value.latitude !== 0
  })
  
  // ===== 核心方法 =====
  
  /**
   * 启动实时定位
   */
  function startTracking() {
    if (isTracking.value) {
      console.log('实时定位已经在运行中')
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      console.log('🎯 启动实时定位服务')
      
      uni.startLocationUpdate({
        success: () => {
          isTracking.value = true
          console.log('✅ 实时定位服务启动成功')
          
          // 开始监听位置变化
          uni.onLocationChange(handleLocationChange)
          
          resolve()
        },
        fail: (error) => {
          console.error('❌ 启动实时定位失败:', error)
          handleMapError('启动实时定位', error)
          reject(error)
        }
      })
    })
  }
  
  /**
   * 停止实时定位
   */
  function stopTracking() {
    if (!isTracking.value) {
      console.log('实时定位未在运行')
      return
    }
    
    console.log('🛑 停止实时定位服务')
    
    try {
      uni.stopLocationUpdate()
      uni.offLocationChange(handleLocationChange)
      isTracking.value = false
      isMoving.value = false
      positionHistory.value = []
      
      console.log('✅ 实时定位服务已停止')
    } catch (error) {
      console.error('❌ 停止实时定位失败:', error)
    }
  }
  

  
  /**
   * 处理位置变化事件
   */
  function handleLocationChange(res) {
    if (!res || !res.longitude || !res.latitude) {
      console.warn('收到无效的位置数据:', res)
      return
    }
    
    const newPosition = {
      longitude: res.longitude,
      latitude: res.latitude,
      accuracy: res.accuracy || 0,
      timestamp: Date.now()
    }
    
    // 更新当前位置
    currentPosition.value = newPosition

    // 检测移动状态
    detectMovement(newPosition)
  }
  

  
  /**
   * 检测移动状态
   */
  function detectMovement(newPosition) {
    // 添加到历史记录
    positionHistory.value.push(newPosition)
    
    // 保持历史记录长度
    if (positionHistory.value.length > MAX_HISTORY_LENGTH) {
      positionHistory.value.shift()
    }
    
    // 需要至少2个位置点才能检测移动
    if (positionHistory.value.length < 2) {
      isMoving.value = false
      return
    }
    
    // 计算与前一个位置的距离
    const lastPosition = positionHistory.value[positionHistory.value.length - 2]
    const distance = calculateDistance(lastPosition, newPosition)
    
    // 更新移动状态
    isMoving.value = distance > MOVEMENT_THRESHOLD
  }
  

  
  /**
   * 计算两点间距离（米）
   */
  function calculateDistance(pos1, pos2) {
    if (!pos1 || !pos2) return 0
    
    const R = 6371000 // 地球半径（米）
    const lat1Rad = pos1.latitude * Math.PI / 180
    const lat2Rad = pos2.latitude * Math.PI / 180
    const deltaLatRad = (pos2.latitude - pos1.latitude) * Math.PI / 180
    const deltaLngRad = (pos2.longitude - pos1.longitude) * Math.PI / 180
    
    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLngRad / 2) * Math.sin(deltaLngRad / 2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    
    return R * c
  }
  
  // ===== 生命周期管理 =====
  
  // 组件卸载时自动停止定位
  onUnmounted(() => {
    if (isTracking.value) {
      stopTracking()
    }
  })
  
  // ===== 返回接口 =====
  
  return {
    // 状态
    isTracking,
    currentPosition,
    hasValidPosition,

    // 方法
    startTracking,
    stopTracking
  }
}
