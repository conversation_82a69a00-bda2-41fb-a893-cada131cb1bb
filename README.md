# 🗺️ 高德地图导航应用

基于 **UniApp + Vue 3 + 高德地图SDK** 开发的跨平台地图导航应用，支持多种出行方式的路线规划和**实时位置跟踪**。

## 1. 项目概述

这是一个功能完整的地图导航应用，集成了高德地图的核心功能，提供了直观的用户界面和流畅的交互体验。应用支持驾车、步行、公交、骑行四种出行方式，并提供详细的路线规划和导航指引。

## 2. 核心功能

### 2.1 地图功能

- **实时定位**：自动获取用户当前位置
- **🆕 实时位置跟踪**：地图蓝色圆圈跟随用户移动实时更新
- **POI搜索**：搜索周边兴趣点和地址
- **地点选择**：支持起点、终点的灵活设置
- **地图展示**：高清地图显示，支持缩放和拖拽

### 2.2 🆕 实时定位功能

- **自动启动**：应用启动时自动开启实时位置跟踪
- **移动状态检测**：自动识别用户是否在移动
- **零API消耗**：实时位置跟踪不消耗高德API额度
- **电量优化**：采用设备原生定位，减少电量消耗

### 2.3 路线规划

- **驾车导航**：提供最优驾车路线，包含距离、时间、费用信息
- **步行导航**：步行路线规划，适合短距离出行
- **公交出行**：公交路线查询，支持跨城市线路
- **骑行导航**：骑行路线规划，绿色出行首选

### 2.4 公交功能

- **🆕 智能出行建议**：自动比较公交与步行时间，优先推荐更便利的方式
- **多方案对比**：提供多个公交出行方案
- **详细信息**：显示换乘站点、运营时间、票价等
- **跨城支持**：支持跨城市公交路线查询
- **实时更新**：动态更新公交信息

## 3. 技术栈

- **前端框架**：Vue 3 + Composition API
- **跨平台框架**：UniApp
- **地图服务**：高德地图 Web API
- **UI组件**：Uni-UI
- **样式预处理**：SCSS
- **开发工具**：HBuilderX

## 4. 项目结构

```
GaodeMap/
├── components/              # 自定义组件
│   ├── Header/             # 导航类型选择头部组件
│   └── InputTip/           # 地址输入提示组件
├── composables/            # 组合式函数
│   ├── useMapState.js      # 地图状态管理
│   ├── useRouteService.js  # 路线服务
│   ├── useLocationService.js # 位置服务
│   └── useRealTimeLocation.js # 🆕 实时定位服务
├── libs/                   # 第三方库
│   ├── amap-wx.130.js     # 高德地图SDK
│   └── config.js          # 配置文件
├── pages/                  # 页面文件
│   └── index/             # 主页面
├── static/                 # 静态资源
├── utils/                  # 工具函数
│   └── mapUtils.js        # 地图工具函数
└── uni_modules/           # UniApp模块
```

## 5. 高德地图SDK方法详解

### 5.1 位置服务相关 (useLocationService.js)

#### 1. `getPoiAround()` - 周边POI搜索

```javascript
// 用途：获取当前位置周边的兴趣点
// 使用位置：pages/index/index.vue - initializeCurrentLocation()
// 功能：应用启动时自动搜索周边POI，为用户提供地点选择
myAmapFun.getPoiAround({
  iconPath: '/static/marker.png',
  iconPathSelected: '/static/marker_checked.png',
  querykeywords: keywords, // 可选：搜索关键词
  success: function(data) {
    // 处理POI数据，显示在地图上
  }
})
```

#### 2. `getInputTips()` - 输入提示

```javascript
// 用途：根据用户输入提供地址搜索建议
// 使用位置：components/InputTip/InputTip.vue
// 功能：实时搜索建议，提升用户输入体验
myAmapFun.getInputTips({
  keywords: inputValue,
  city: currentCity,
  success: function(data) {
    // 显示搜索建议列表
  }
})
```

#### 3. `getGeocode()` - 地理编码

```javascript
// 用途：将地址转换为经纬度坐标
// 使用位置：useLocationService.js - getGeocode()
// 功能：用户选择地址后，获取精确坐标用于路线规划
myAmapFun.getGeocode({
  address: userAddress,
  city: cityName,
  success: function(data) {
    // 获取坐标信息
  }
})
```

#### 4. `getRegeocode()` - 逆地理编码

```javascript
// 用途：将经纬度坐标转换为地址信息
// 使用位置：useLocationService.js - getRegeocode()
// 功能：获取用户当前位置的详细地址描述
myAmapFun.getRegeocode({
  location: longitude + ',' + latitude,
  success: function(data) {
    // 获取地址描述
  }
})
```

### 5.2 路线规划相关 (useRouteService.js)

#### 5. `getDrivingRoute()` - 驾车路线规划

```javascript
// 用途：计算两点间的驾车路线
// 使用位置：useRouteService.js - getDrivingRoute()
// 功能：提供驾车导航路线，包含距离、时间、费用
myAmapFun.getDrivingRoute({
  origin: startPoint,      // 起点坐标
  destination: endPoint,   // 终点坐标
  success: function(data) {
    // 处理驾车路线数据
    // 包含：路径点、距离、出租车费用
  }
})
```

#### 6. `getWalkingRoute()` - 步行路线规划

```javascript
// 用途：计算两点间的步行路线
// 使用位置：useRouteService.js - getWalkingRoute()
// 功能：提供步行导航路线，适合短距离出行
myAmapFun.getWalkingRoute({
  origin: startPoint,
  destination: endPoint,
  success: function(data) {
    // 处理步行路线数据
    // 包含：路径点、距离、预计时间
  }
})
```

#### 7. `getTransitRoute()` - 公交路线规划

```javascript
// 用途：计算两点间的公交路线
// 使用位置：useRouteService.js - getTransitRoute()
// 功能：提供公交出行方案，支持跨城市查询
myAmapFun.getTransitRoute({
  origin: startPoint,
  destination: endPoint,
  city: startCity,         // 起点城市
  cityd: endCity,         // 终点城市（跨城时使用）
  success: function(data) {
    // 处理公交路线数据
    // 包含：多个方案、换乘信息、票价、时间
  }
})
```

#### 8. `getRidingRoute()` - 骑行路线规划

```javascript
// 用途：计算两点间的骑行路线
// 使用位置：useRouteService.js - getRidingRoute()
// 功能：提供骑行导航路线，绿色出行选择
myAmapFun.getRidingRoute({
  origin: startPoint,
  destination: endPoint,
  success: function(data) {
    // 处理骑行路线数据
    // 包含：路径点、距离、预计时间
  }
})
```

### 5.3 🆕 实时定位相关 (useRealTimeLocation.js)

#### 9. `uni.startLocationUpdate()` - 启动实时定位

```javascript
// 用途：启动设备的实时位置更新服务
// 使用位置：useRealTimeLocation.js - startTracking()
// 功能：开启GPS实时定位，不消耗高德API额度
uni.startLocationUpdate({
  success: () => {
    // 定位服务启动成功
    uni.onLocationChange(handleLocationChange)
  }
})
```

#### 10. `uni.onLocationChange()` - 监听位置变化

```javascript
// 用途：监听设备位置变化事件
// 使用位置：useRealTimeLocation.js - handleLocationChange()
// 功能：实时获取位置坐标，让地图蓝色圆圈跟随移动
uni.onLocationChange((res) => {
  // 获取实时坐标，不消耗API
  currentPosition.value = {
    longitude: res.longitude,
    latitude: res.latitude,
    accuracy: res.accuracy
  }
})
```

#### 11. `uni.stopLocationUpdate()` - 停止实时定位

```javascript
// 用途：停止设备的实时位置更新服务
// 使用位置：useRealTimeLocation.js - stopTracking()
// 功能：节省电量，停止位置监听
uni.stopLocationUpdate()
```

## 6. 🆕 实时定位功能详解

### 6.1 功能特点

- **零API消耗**：实时位置跟踪本身不消耗高德API调用量
- **自动启动**：应用启动时自动开启，无需用户手动操作
- **电量优化**：采用设备原生定位，比频繁调用网络API更省电
- **移动检测**：自动识别用户移动状态

### 6.2 使用方法

1. **自动启动**：应用启动时自动开启实时位置跟踪
2. **地图跟随**：地图中心和蓝色定位圆圈会跟随您的移动实时更新
3. **自动停止**：应用退出时自动停止实时定位

### 6.3 技术实现原理

```javascript
// 应用启动时自动开启实时定位
onLoad(async () => {
  initializeCurrentLocation()

  // 自动开启实时定位
  try {
    await startTracking()
    console.log('✅ 实时定位已自动开启')
  } catch (error) {
    console.warn('⚠️ 实时定位开启失败:', error)
  }
})

// 监听位置变化，更新地图中心
watchEffect(() => {
  if (isTracking.value && hasValidPosition.value) {
    const pos = currentPosition.value

    // 更新地图中心（不消耗API）
    longitude.value = pos.longitude
    latitude.value = pos.latitude
  }
})
```

### 6.4 优化策略

- **自动管理**：启动时自动开启，退出时自动停止
- **零API消耗**：位置跟踪不调用高德API
- **移动检测**：自动识别用户移动状态
- **电量优化**：使用设备原生定位服务

## 7. 🆕 智能公交建议功能

### 7.1 功能特点

- **智能时间比较**：自动获取公交和步行路线，比较耗时
- **用户友好提示**：当步行更便利时，主动提醒用户
- **灵活选择**：用户可选择接受建议或继续查看公交路线
- **零额外消耗**：利用现有API调用，不增加额外成本

### 7.2 工作原理

```javascript
// 公交路线规划时的智能比较逻辑
function getTransitRoute(params, onSuccess, onFail) {
  // 1. 先获取步行路线作为基准
  myAmapFun.getWalkingRoute({
    success: function(walkingData) {
      const walkingDuration = walkingData.paths?.[0]?.duration || 0

      // 2. 再获取公交路线
      myAmapFun.getTransitRoute({
        success: function(transitData) {
          const transitDuration = transitData.transits[0].duration || 0

          // 3. 智能比较并给出建议
          if (transitDuration > walkingDuration && walkingDuration > 0) {
            // 步行更便利，显示建议弹窗
            showWalkingSuggestion(walkingDuration, transitDuration)
          } else {
            // 公交更便利，显示公交路线
            showTransitRoutes(transitData.transits)
          }
        }
      })
    }
  })
}
```

### 7.3 用户体验

1. **自动检测**：用户选择公交出行时自动进行时间比较
2. **友好提示**：弹窗显示步行和公交的具体时间对比
3. **自由选择**：
   - 点击"切换步行"：自动切换到步行导航
   - 点击"继续查看公交"：显示完整公交路线方案
4. **无感知优化**：整个过程对用户透明，提升出行效率

## 8. 快速开始

### 8.1 环境要求

- Node.js 14+
- HBuilderX 3.0+
- 高德地图开发者账号

### 8.2 安装步骤

1. **克隆项目**

```bash
git clone [项目地址]
cd GaodeMap
```

2. **配置高德地图Key**

```javascript
// 修改 libs/config.js
const config = {
  key: "你的高德地图API Key"
}
```

3. **运行项目**

- 使用HBuilderX打开项目
- 选择运行到微信小程序/App/H5

### 8.3 获取高德地图API Key

1. 访问 [高德开放平台](https://lbs.amap.com/)
2. 注册开发者账号
3. 创建应用，选择对应平台
4. 获取API Key并配置到项目中

## 9. 支持平台

- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ H5
- ✅ Android App
- ✅ iOS App

## 10. 界面预览

### 10.1 主要功能界面

- **地图主界面**：显示当前位置和周边POI
- **🆕 实时定位跟踪**：自动开启，地图实时跟随移动
- **🆕 智能公交建议**：自动比较出行时间，推荐最优方式
- **路线规划**：支持四种出行方式切换
- **公交方案**：详细的公交换乘信息
- **导航界面**：实时导航指引

## 11. 开发说明

### 11.1 核心组件

- **Header组件**：导航类型选择（驾车/步行/公交/骑行）
- **InputTip组件**：地址输入和搜索建议

### 11.2 状态管理架构

- **useMapState.js**：地图状态管理
- **useRouteService.js**：路线服务（🆕 新增智能公交建议）
- **useLocationService.js**：位置服务
- **🆕 useRealTimeLocation.js**：实时定位服务

## 12. 高德SDK方法使用统计

项目中共使用了 **11个** 核心API方法：

| 方法名                           | 调用次数 | 主要用途     | API消耗   |
| -------------------------------- | -------- | ------------ | --------- |
| `getPoiAround()`               | 3次      | 周边POI搜索  | ✅ 消耗   |
| `getInputTips()`               | 1次      | 输入提示     | ✅ 消耗   |
| `getGeocode()`                 | 1次      | 地理编码     | ✅ 消耗   |
| `getRegeocode()`               | 1次      | 逆地理编码   | ✅ 消耗   |
| `getDrivingRoute()`            | 1次      | 驾车路线     | ✅ 消耗   |
| `getWalkingRoute()`            | 1次      | 步行路线     | ✅ 消耗   |
| `getTransitRoute()`            | 1次      | 公交路线     | ✅ 消耗   |
| `getRidingRoute()`             | 1次      | 骑行路线     | ✅ 消耗   |
| `🆕 uni.startLocationUpdate()` | 1次      | 启动实时定位 | ❌ 不消耗 |
| `🆕 uni.onLocationChange()`    | 1次      | 监听位置变化 | ❌ 不消耗 |
| `🆕 uni.stopLocationUpdate()`  | 1次      | 停止实时定位 | ❌ 不消耗 |

## 13. 配置说明

### 13.1 必需配置

```javascript
// libs/config.js
const config = {
  key: "你的高德地图API Key" // 必须配置
}
```

### 13.2 小程序权限配置

在`manifest.json`中配置位置权限：
```json
"mp-weixin": {
  "permission": {
    "scope.userLocation": {
      "desc": "你的位置信息将用于小程序位置接口的效果展示"
    }
  },
  "requiredPrivateInfos": [
    "getLocation",
    "startLocationUpdate",
    "onLocationChange"
  ]
}
```

### 13.3 API权限要求

确保高德地图API Key具有以下权限：

- ✅ Web服务API
- ✅ 地点搜索
- ✅ 路径规划
- ✅ 输入提示
- ✅ 地理编码

## 14. 许可证

MIT License

## 15. 联系方式

如有问题，请通过以下方式联系：
- 提交 GitHub Issue
- 发送邮件至：[<EMAIL>]

---

**注意**：使用前请确保已获取有效的高德地图API Key，并根据实际需求配置相应的权限和配额。
