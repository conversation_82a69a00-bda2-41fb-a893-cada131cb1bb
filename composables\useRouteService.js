/**
 * 路线服务组合式函数
 * 处理各种类型的路线规划（驾车、步行、公交、骑行）
 */

import AMapWX from '../libs/amap-wx.130.js'
import gaode_key from '../libs/config.js'
import { 
  parseRoutePoints, 
  parseRidingPoints, 
  createPolyline,
  formatDistance,
  formatDuration,
  formatTaxiCost,
  processTransitData,
  handleMapError
} from '../utils/mapUtils.js'

export function useRouteService() {
  
  /**
   * 创建高德地图实例
   * @returns {object} 高德地图实例
   */
  function createAmapInstance() {
    return new AMapWX({
      key: gaode_key.config.key
    })
  }
  
  /**
   * 驾车路线规划
   */
  function getDrivingRoute(params, onSuccess, onFail) {
    const { origin, destination } = params

    if (!origin || !destination) return

    const myAmapFun = createAmapInstance()

    myAmapFun.getDrivingRoute({
      origin,
      destination,
      success: function(data) {
        const points = parseRoutePoints(data.paths?.[0]?.steps || [])
        const polylineData = createPolyline(points)

        const routeInfo = {
          distance: data.paths?.[0]?.distance ? formatDistance(data.paths[0].distance) : '',
          cost: data.taxi_cost ? formatTaxiCost(data.taxi_cost) : '',
          polyline: polylineData
        }

        onSuccess?.(routeInfo)
      },
      fail: function(error) {
        handleMapError('驾车路线规划', error)
        onFail?.(error)
      }
    })
  }
  
  /**
   * 步行路线规划
   */
  function getWalkingRoute(params, onSuccess, onFail) {
    const { origin, destination } = params

    if (!origin || !destination) return

    const myAmapFun = createAmapInstance()

    myAmapFun.getWalkingRoute({
      origin,
      destination,
      success: function(data) {
        const points = parseRoutePoints(data.paths?.[0]?.steps || [])
        const polylineData = createPolyline(points)

        const routeInfo = {
          distance: data.paths?.[0]?.distance ? formatDistance(data.paths[0].distance) : '',
          cost: data.paths?.[0]?.duration ? formatDuration(data.paths[0].duration) : '',
          polyline: polylineData
        }

        onSuccess?.(routeInfo)
      },
      fail: function(error) {
        handleMapError('步行路线规划', error)
        onFail?.(error)
      }
    })
  }
  
  /**
   * 公交路线规划（智能比较步行时间）
   */
  function getTransitRoute(params, onSuccess, onFail) {
    const { origin, destination, city = '广州', cityd } = params

    if (!origin || !destination) return

    const myAmapFun = createAmapInstance()

    // 先获取步行路线作为对比基准
    myAmapFun.getWalkingRoute({
      origin,
      destination,
      success: function(walkingData) {
        const walkingDuration = Number(walkingData.paths?.[0]?.duration) || 0

        // 再获取公交路线
        const requestParams = {
          origin,
          destination,
          city
        }

        if (cityd && cityd !== city) {
          Object.assign(requestParams, { cityd })
        }

        myAmapFun.getTransitRoute({
          ...requestParams,
          success: function(transitData) {
            if (transitData?.transits && transitData.transits.length > 0) {
              // 获取最快的公交方案（通常是第一个）
              const fastestTransit = transitData.transits[0]
              const transitDuration = Number(fastestTransit.duration) || 0

              // 调试信息：打印时间对比
              console.log('🚶‍♂️ 步行时间:', walkingDuration, '秒 (', Math.round(walkingDuration / 60), '分钟)', typeof walkingDuration)
              console.log('🚌 公交时间:', transitDuration, '秒 (', Math.round(transitDuration / 60), '分钟)', typeof transitDuration)
              console.log('🔍 原始数据:', {
                walkingRaw: walkingData.paths?.[0]?.duration,
                transitRaw: fastestTransit.duration
              })

              // 比较公交和步行时间
              console.log('🔍 条件判断:', {
                'transitDuration > walkingDuration': transitDuration > walkingDuration,
                'walkingDuration > 0': walkingDuration > 0,
                '最终判断': transitDuration > walkingDuration && walkingDuration > 0
              })

              if (transitDuration > walkingDuration && walkingDuration > 0) {
                // 公交时间更长，返回步行建议
                console.log('✅ 触发步行建议')
                onFail?.({
                  message: '步行更加便利',
                  walkingTime: Math.round(walkingDuration / 60),
                  transitTime: Math.round(transitDuration / 60),
                  showWalkingSuggestion: true
                })
              } else {
                // 公交时间更短或相当，返回公交路线
                console.log('❌ 显示公交路线')
                const processedTransits = processTransitData(transitData.transits)
                onSuccess?.({ transits: processedTransits })
              }
            } else {
              const errorMessage = cityd && cityd !== city
                ? `未找到从${city}到${cityd}的公交路线，建议选择其他交通方式`
                : '未找到公交路线'
              onFail?.({ message: errorMessage })
            }
          },
          fail: function(error) {
            handleMapError('公交路线规划', error)
            onFail?.(error)
          }
        })
      },
      fail: function(error) {
        // 步行路线获取失败，仍然尝试获取公交路线
        console.warn('步行路线获取失败，继续获取公交路线:', error)

        const requestParams = {
          origin,
          destination,
          city
        }

        if (cityd && cityd !== city) {
          Object.assign(requestParams, { cityd })
        }

        myAmapFun.getTransitRoute({
          ...requestParams,
          success: function(data) {
            if (data?.transits) {
              const processedTransits = processTransitData(data.transits)
              onSuccess?.({ transits: processedTransits })
            } else {
              const errorMessage = cityd && cityd !== city
                ? `未找到从${city}到${cityd}的公交路线，建议选择其他交通方式`
                : '未找到公交路线'
              onFail?.({ message: errorMessage })
            }
          },
          fail: function(error) {
            handleMapError('公交路线规划', error)
            onFail?.(error)
          }
        })
      }
    })
  }
  
  /**
   * 骑行路线规划
   */
  function getRidingRoute(params, onSuccess, onFail) {
    const { origin, destination } = params

    if (!origin || !destination) return

    const myAmapFun = createAmapInstance()

    myAmapFun.getRidingRoute({
      origin,
      destination,
      success: function(data) {
        if (data?.paths?.[0]) {
          const path = data.paths[0]
          const points = parseRidingPoints(path.rides || [])
          const polylineData = createPolyline(points)

          const routeInfo = {
            distance: path.distance ? formatDistance(path.distance) : '',
            cost: path.duration ? formatDuration(path.duration) : '',
            polyline: polylineData
          }

          onSuccess?.(routeInfo)
        } else {
          onFail?.({ message: '未找到骑行路线' })
        }
      },
      fail: function(error) {
        handleMapError('骑行路线规划', error)
        onFail?.(error)
      }
    })
  }
  
  /**
   * 统一的路线规划接口
   */
  function planRoute(routeType, params, onSuccess, onFail) {
    switch (routeType) {
      case 'car':
        getDrivingRoute(params, onSuccess, onFail)
        break
      case 'walk':
        getWalkingRoute(params, onSuccess, onFail)
        break
      case 'bus':
        getTransitRoute(params, onSuccess, onFail)
        break
      case 'riding':
        getRidingRoute(params, onSuccess, onFail)
        break
      default:
        onFail?.({ message: '不支持的路线类型' })
    }
  }
  
  // 返回方法
  return {
    getDrivingRoute,
    getWalkingRoute,
    getTransitRoute,
    getRidingRoute,
    planRoute
  }
}
