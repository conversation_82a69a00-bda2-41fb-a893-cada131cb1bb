"use strict";const n=require("../common/vendor.js"),p=require("../utils/mapUtils.js");function y(){const i=n.ref(!1),l=n.ref({longitude:0,latitude:0,accuracy:0,timestamp:0}),c=n.ref(!1),a=n.ref([]),h=10,g=5,v=n.computed(()=>l.value.longitude!==0&&l.value.latitude!==0);function M(){return i.value?(console.log("实时定位已经在运行中"),Promise.resolve()):new Promise((t,e)=>{console.log("🎯 启动实时定位服务"),n.index.startLocationUpdate({success:()=>{i.value=!0,console.log("✅ 实时定位服务启动成功"),n.index.onLocationChange(s),t()},fail:o=>{console.error("❌ 启动实时定位失败:",o),p.handleMapError("启动实时定位",o),e(o)}})})}function u(){if(!i.value){console.log("实时定位未在运行");return}console.log("🛑 停止实时定位服务");try{n.index.stopLocationUpdate(),n.index.offLocationChange(s),i.value=!1,c.value=!1,a.value=[],console.log("✅ 实时定位服务已停止")}catch(t){console.error("❌ 停止实时定位失败:",t)}}function s(t){if(!t||!t.longitude||!t.latitude){console.warn("收到无效的位置数据:",t);return}const e={longitude:t.longitude,latitude:t.latitude,accuracy:t.accuracy||0,timestamp:Date.now()};l.value=e,m(e)}function m(t){if(a.value.push(t),a.value.length>g&&a.value.shift(),a.value.length<2){c.value=!1;return}const e=a.value[a.value.length-2],o=L(e,t);c.value=o>h}function L(t,e){if(!t||!e)return 0;const o=6371e3,P=t.latitude*Math.PI/180,R=e.latitude*Math.PI/180,r=(e.latitude-t.latitude)*Math.PI/180,d=(e.longitude-t.longitude)*Math.PI/180,f=Math.sin(r/2)*Math.sin(r/2)+Math.cos(P)*Math.cos(R)*Math.sin(d/2)*Math.sin(d/2),T=2*Math.atan2(Math.sqrt(f),Math.sqrt(1-f));return o*T}return n.onUnmounted(()=>{i.value&&u()}),{isTracking:i,currentPosition:l,hasValidPosition:v,startTracking:M,stopTracking:u}}exports.useRealTimeLocation=y;
