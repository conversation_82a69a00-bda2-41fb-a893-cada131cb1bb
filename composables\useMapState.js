/**
 * 地图状态管理组合式函数
 * 管理地图的基本状态和标记点
 */

import { ref, computed } from 'vue'
import { 
  parseLocation, 
  createMarker, 
  createStartMarker, 
  createEndMarker,
  createIncludePoints,
  handleMapError
} from '../utils/mapUtils.js'

export function useMapState() {
  // ===== 响应式状态 =====
  
  // 地图基本状态
  const mapState = ref(true) // 地图显示状态
  const markers = ref([]) // 地图标记点
  const polyline = ref([]) // 路线数据
  const includePoints = ref([]) // 地图包含的点
  
  // 位置信息
  const latitude = ref('') // 当前纬度
  const longitude = ref('') // 当前经度
  const city = ref('') // 当前城市
  
  // 目的地信息
  const latitude_e = ref('') // 目的地纬度
  const longitude_e = ref('') // 目的地经度
  const city_e = ref('') // 目的地城市
  const mapEndObj = ref({}) // 目的地完整信息
  
  // 显示信息
  const textData = ref({}) // 当前显示的文本信息
  const distance = ref('') // 距离信息
  const cost = ref('') // 费用/时间信息
  const daohang = ref(false) // 是否处于导航状态
  
  // 导航类型
  const gaode_type = ref('car') // 当前导航类型：car, walk, bus, riding
  
  // 公交信息
  const transits = ref([]) // 公交路线信息
  
  // ===== 计算属性 =====
  
  // 当前位置坐标字符串
  const currentLocation = computed(() => {
    if (!longitude.value || !latitude.value) return ''
    return `${longitude.value},${latitude.value}`
  })
  
  // 目的地坐标字符串
  const destinationLocation = computed(() => {
    if (!longitude_e.value || !latitude_e.value) return ''
    return `${longitude_e.value},${latitude_e.value}`
  })
  
  // 是否有有效的起点和终点
  const hasValidRoute = computed(() => {
    return currentLocation.value && destinationLocation.value
  })
  
  // ===== 方法 =====
  
  /**
   * 设置当前位置
   */
  function setCurrentLocation(locationInfo) {
    if (!locationInfo) return

    let locationString = locationInfo.location

    if (!locationString) {
      if (locationInfo.longitude && locationInfo.latitude) {
        locationString = `${locationInfo.longitude},${locationInfo.latitude}`
      } else {
        return
      }
    }

    const { longitude: lng, latitude: lat } = parseLocation(locationString)

    if (lng === 0 && lat === 0) return

    longitude.value = lng
    latitude.value = lat
    city.value = extractCityFromLocationInfo(locationInfo)
    includePoints.value = createIncludePoints(locationString)
  }
  
  /**
   * 设置目的地位置
   */
  function setDestination(locationInfo) {
    if (!locationInfo?.location) return

    const { longitude: lng, latitude: lat } = parseLocation(locationInfo.location)

    if (lng === 0 && lat === 0) return

    longitude_e.value = lng
    latitude_e.value = lat
    city_e.value = extractCityFromLocationInfo(locationInfo)
    mapEndObj.value = locationInfo
    daohang.value = true
  }
  
  /**
   * 设置路线标记点（起点和终点）
   */
  function setRouteMarkers() {
    if (!hasValidRoute.value) return

    const startMarker = createStartMarker({
      location: currentLocation.value,
      name: city.value || '起点',
      district: '当前位置'
    })

    const endMarker = createEndMarker({
      location: destinationLocation.value,
      name: mapEndObj.value.name || city_e.value || '终点',
      district: mapEndObj.value.district || '目的地'
    })

    markers.value = [startMarker, endMarker]
    includePoints.value = createIncludePoints(
      currentLocation.value,
      destinationLocation.value
    )

    showMarkerInfo([startMarker, endMarker], 1)
  }
  
  /**
   * 显示标记点信息
   */
  function showMarkerInfo(markersData, index) {
    if (!Array.isArray(markersData) || !markersData[index]) return

    const marker = markersData[index]
    textData.value = {
      name: marker.name || '未知位置',
      desc: marker.address || '暂无地址信息'
    }
  }

  /**
   * 改变标记点颜色（选中状态）
   */
  function changeMarkerColor(markersData, selectedIndex) {
    if (!Array.isArray(markersData)) return

    const updatedMarkers = markersData.map((marker, index) => ({
      ...marker,
      iconPath: index === selectedIndex
        ? '/static/marker_checked.png'
        : '/static/marker.png'
    }))

    markers.value = updatedMarkers
  }
  
  /**
   * 重置地图状态
   */
  function resetMapState() {
    mapState.value = true
    markers.value = []
    polyline.value = []
    includePoints.value = []
    textData.value = {}
    distance.value = ''
    cost.value = ''
    daohang.value = false

    // 清除目的地信息
    latitude_e.value = ''
    longitude_e.value = ''
    city_e.value = ''
    mapEndObj.value = {}

    // 清除公交信息
    transits.value = []
  }

  /**
   * 切换导航类型
   */
  function changeNavigationType(type) {
    const validTypes = ['car', 'walk', 'bus', 'riding']
    if (!validTypes.includes(type)) return

    gaode_type.value = type
    mapState.value = type !== 'bus'
  }

  // ===== 辅助函数 =====

  /**
   * 从位置信息中提取城市名称
   */
  function extractCityFromLocationInfo(locationInfo) {
    if (!locationInfo) return ''

    let cityName = ''

    if (locationInfo.district) {
      const districtParts = locationInfo.district.split(/[市区县]/)
      if (districtParts.length > 0 && districtParts[0]) {
        cityName = districtParts[0] + '市'
      } else {
        cityName = locationInfo.district
      }
    } else if (locationInfo.formatted_address) {
      const addressParts = locationInfo.formatted_address.split(/[市区县]/)
      if (addressParts.length > 0 && addressParts[0]) {
        cityName = addressParts[0] + '市'
      }
    } else if (locationInfo.name) {
      cityName = locationInfo.name
    }

    return cityName
  }

  // ===== 返回状态和方法 =====
  return {
    // 状态
    mapState,
    markers,
    polyline,
    includePoints,
    latitude,
    longitude,
    city,
    latitude_e,
    longitude_e,
    city_e,
    mapEndObj,
    textData,
    distance,
    cost,
    daohang,
    gaode_type,
    transits,

    // 计算属性
    currentLocation,
    destinationLocation,
    hasValidRoute,

    // 方法
    setCurrentLocation,
    setDestination,
    setRouteMarkers,
    showMarkerInfo,
    changeMarkerColor,
    resetMapState,
    changeNavigationType
  }
}
