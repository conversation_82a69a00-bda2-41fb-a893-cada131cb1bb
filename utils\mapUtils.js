/**
 * 地图工具函数集合
 * 提供坐标转换、数据格式化等通用功能
 */

/**
 * 解析位置字符串为经纬度对象
 */
export function parseLocation(location) {
  if (!location || typeof location !== 'string' || location.trim() === '') {
    return { longitude: 0, latitude: 0 }
  }

  const parts = location.trim().split(',')

  if (parts.length !== 2) {
    return { longitude: 0, latitude: 0 }
  }

  const longitude = parseFloat(parts[0].trim())
  const latitude = parseFloat(parts[1].trim())

  if (isNaN(longitude) || isNaN(latitude)) {
    return { longitude: 0, latitude: 0 }
  }

  if (longitude < -180 || longitude > 180 || latitude < -90 || latitude > 90) {
    return { longitude: 0, latitude: 0 }
  }

  return { longitude, latitude }
}

/**
 * 创建起点标记
 */
export function createStartMarker(locationInfo) {
  const { longitude, latitude } = parseLocation(locationInfo.location)

  return {
    id: 0,
    latitude,
    longitude,
    iconPath: '/static/mapicon_navi_s.png',
    width: 23,
    height: 33,
    name: locationInfo.name || '起点',
    address: locationInfo.district || '当前位置'
  }
}

/**
 * 创建终点标记
 */
export function createEndMarker(locationInfo) {
  const { longitude, latitude } = parseLocation(locationInfo.location)

  return {
    id: 1,
    latitude,
    longitude,
    iconPath: '/static/mapicon_navi_e.png',
    width: 24,
    height: 34,
    name: locationInfo.name || '终点',
    address: locationInfo.district || '目的地'
  }
}

/**
 * 解析路线数据为地图可用的点集合
 */
export function parseRoutePoints(steps) {
  const points = []

  if (!Array.isArray(steps)) return points

  steps.forEach(step => {
    if (step.polyline) {
      const polylinePoints = step.polyline.split(';')
      polylinePoints.forEach(point => {
        const [lng, lat] = point.split(',')
        if (lng && lat) {
          points.push({
            longitude: parseFloat(lng),
            latitude: parseFloat(lat)
          })
        }
      })
    }
  })

  return points
}

/**
 * 解析骑行路线数据
 */
export function parseRidingPoints(rides) {
  const points = []

  if (!Array.isArray(rides)) return points

  rides.forEach(ride => {
    if (ride.polyline) {
      const polylinePoints = ride.polyline.split(';')
      polylinePoints.forEach(point => {
        const [lng, lat] = point.split(',')
        if (lng && lat) {
          points.push({
            longitude: parseFloat(lng),
            latitude: parseFloat(lat)
          })
        }
      })
    }
  })

  return points
}

/**
 * 创建路线样式配置
 * @param {array} points - 路线点集合
 * @param {string} color - 路线颜色，默认蓝色
 * @param {number} width - 路线宽度，默认6
 * @returns {array} 路线配置数组
 */
export function createPolyline(points, color = '#0091ff', width = 6) {
  return [{
    points,
    color,
    width
  }]
}

/**
 * 格式化距离显示
 */
export function formatDistance(distance) {
  if (!distance || isNaN(distance)) return '0米'

  const distanceNum = parseInt(distance)
  return distanceNum >= 1000
    ? `${(distanceNum / 1000).toFixed(1)}公里`
    : `${distanceNum}米`
}

/**
 * 格式化时间显示
 */
export function formatDuration(duration) {
  if (!duration || isNaN(duration)) return '0分钟'

  const minutes = Math.round(duration / 60)
  if (minutes >= 60) {
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }
  return `${minutes}分钟`
}

/**
 * 格式化打车费用显示
 */
export function formatTaxiCost(cost) {
  return (!cost || isNaN(cost)) ? '' : `打车约${parseInt(cost)}元`
}

/**
 * 处理公交路线数据
 */
export function processTransitData(transits) {
  if (!Array.isArray(transits)) return []

  return transits.map(transit => {
    const segments = transit.segments || []
    const transport = []

    segments.forEach((segment, index) => {
      if (segment.bus?.buslines?.[0]?.name) {
        let name = segment.bus.buslines[0].name
        if (index !== 0) {
          name = '--' + name
        }
        transport.push(name)
      }
    })

    return { ...transit, transport }
  })
}

/**
 * 创建地图包含点配置
 */
export function createIncludePoints(startLocation, endLocation = null) {
  const points = []

  if (startLocation) {
    const { longitude, latitude } = parseLocation(startLocation)
    points.push({ latitude, longitude })
  }

  if (endLocation) {
    const { longitude, latitude } = parseLocation(endLocation)
    points.push({ latitude, longitude })
  }

  return points
}

/**
 * 验证POI标记点数据的有效性
 */
export function validateMarkerData(marker) {
  if (!marker) return false

  if (typeof marker.latitude !== 'number' || typeof marker.longitude !== 'number') {
    return false
  }

  if (isNaN(marker.latitude) || isNaN(marker.longitude)) {
    return false
  }

  if (marker.latitude < -90 || marker.latitude > 90 ||
      marker.longitude < -180 || marker.longitude > 180) {
    return false
  }

  if (marker.latitude === 0 && marker.longitude === 0) {
    return false
  }

  return true
}

/**
 * 安全地过滤和格式化标记点数据
 */
export function safeFilterMarkers(markers) {
  return Array.isArray(markers) ? markers.filter(validateMarkerData) : []
}

/**
 * 统一的错误处理函数
 */
export function handleMapError(_, error) {
  const errorMessage = error?.errMsg || error?.message || '操作失败，请重试'
  uni.showToast({
    title: errorMessage,
    icon: 'none',
    duration: 2000
  })
}
