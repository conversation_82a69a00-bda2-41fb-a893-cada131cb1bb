"use strict";const f=require("../common/vendor.js");function u(t){if(!t||typeof t!="string"||t.trim()==="")return{longitude:0,latitude:0};const n=t.trim().split(",");if(n.length!==2)return{longitude:0,latitude:0};const i=parseFloat(n[0].trim()),e=parseFloat(n[1].trim());return isNaN(i)||isNaN(e)?{longitude:0,latitude:0}:i<-180||i>180||e<-90||e>90?{longitude:0,latitude:0}:{longitude:i,latitude:e}}function d(t){const{longitude:n,latitude:i}=u(t.location);return{id:0,latitude:i,longitude:n,iconPath:"/static/mapicon_navi_s.png",width:23,height:33,name:t.name||"起点",address:t.district||"当前位置"}}function p(t){const{longitude:n,latitude:i}=u(t.location);return{id:1,latitude:i,longitude:n,iconPath:"/static/mapicon_navi_e.png",width:24,height:34,name:t.name||"终点",address:t.district||"目的地"}}function g(t){const n=[];return Array.isArray(t)&&t.forEach(i=>{i.polyline&&i.polyline.split(";").forEach(a=>{const[s,r]=a.split(",");s&&r&&n.push({longitude:parseFloat(s),latitude:parseFloat(r)})})}),n}function h(t){const n=[];return Array.isArray(t)&&t.forEach(i=>{i.polyline&&i.polyline.split(";").forEach(a=>{const[s,r]=a.split(",");s&&r&&n.push({longitude:parseFloat(s),latitude:parseFloat(r)})})}),n}function y(t,n="#0091ff",i=6){return[{points:t,color:n,width:i}]}function m(t){if(!t||isNaN(t))return"0米";const n=parseInt(t);return n>=1e3?`${(n/1e3).toFixed(1)}公里`:`${n}米`}function N(t){if(!t||isNaN(t))return"0分钟";const n=Math.round(t/60);if(n>=60){const i=Math.floor(n/60),e=n%60;return e>0?`${i}小时${e}分钟`:`${i}小时`}return`${n}分钟`}function M(t){return!t||isNaN(t)?"":`打车约${parseInt(t)}元`}function P(t){return Array.isArray(t)?t.map(n=>{const i=n.segments||[],e=[];return i.forEach((a,s)=>{var r,l,c;if((c=(l=(r=a.bus)==null?void 0:r.buslines)==null?void 0:l[0])!=null&&c.name){let o=a.bus.buslines[0].name;s!==0&&(o="--"+o),e.push(o)}}),{...n,transport:e}}):[]}function E(t,n=null){const i=[];if(t){const{longitude:e,latitude:a}=u(t);i.push({latitude:a,longitude:e})}if(n){const{longitude:e,latitude:a}=u(n);i.push({latitude:a,longitude:e})}return i}function F(t){return!(!t||typeof t.latitude!="number"||typeof t.longitude!="number"||isNaN(t.latitude)||isNaN(t.longitude)||t.latitude<-90||t.latitude>90||t.longitude<-180||t.longitude>180||t.latitude===0&&t.longitude===0)}function A(t){return Array.isArray(t)?t.filter(F):[]}function D(t,n){const i=(n==null?void 0:n.errMsg)||(n==null?void 0:n.message)||"操作失败，请重试";f.index.showToast({title:i,icon:"none",duration:2e3})}exports.createEndMarker=p;exports.createIncludePoints=E;exports.createPolyline=y;exports.createStartMarker=d;exports.formatDistance=m;exports.formatDuration=N;exports.formatTaxiCost=M;exports.handleMapError=D;exports.parseLocation=u;exports.parseRidingPoints=h;exports.parseRoutePoints=g;exports.processTransitData=P;exports.safeFilterMarkers=A;
