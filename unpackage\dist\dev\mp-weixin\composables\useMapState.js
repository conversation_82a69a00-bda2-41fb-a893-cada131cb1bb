"use strict";const r=require("../common/vendor.js"),u=require("../utils/mapUtils.js");function j(){const g=r.ref(!0),l=r.ref([]),h=r.ref([]),c=r.ref([]),d=r.ref(""),v=r.ref(""),k=r.ref(""),s=r.ref(""),n=r.ref(""),o=r.ref(""),i=r.ref({}),y=r.ref({}),_=r.ref(""),L=r.ref(""),M=r.ref(!1),P=r.ref("car"),S=r.ref([]),f=r.computed(()=>!v.value||!d.value?"":`${v.value},${d.value}`),m=r.computed(()=>!n.value||!s.value?"":`${n.value},${s.value}`),$=r.computed(()=>f.value&&m.value);function C(e){if(!e)return;let t=e.location;if(!t)if(e.longitude&&e.latitude)t=`${e.longitude},${e.latitude}`;else return;const{longitude:a,latitude:p}=u.parseLocation(t);a===0&&p===0||(v.value=a,d.value=p,k.value=b(e),c.value=u.createIncludePoints(t))}function T(e){if(!(e!=null&&e.location))return;const{longitude:t,latitude:a}=u.parseLocation(e.location);t===0&&a===0||(n.value=t,s.value=a,o.value=b(e),i.value=e,M.value=!0)}function q(){if(!$.value)return;const e=u.createStartMarker({location:f.value,name:k.value||"起点",district:"当前位置"}),t=u.createEndMarker({location:m.value,name:i.value.name||o.value||"终点",district:i.value.district||"目的地"});l.value=[e,t],c.value=u.createIncludePoints(f.value,m.value),A([e,t],1)}function A(e,t){if(!Array.isArray(e)||!e[t])return;const a=e[t];y.value={name:a.name||"未知位置",desc:a.address||"暂无地址信息"}}function w(e,t){if(!Array.isArray(e))return;const a=e.map((p,R)=>({...p,iconPath:R===t?"/static/marker_checked.png":"/static/marker.png"}));l.value=a}function E(){g.value=!0,l.value=[],h.value=[],c.value=[],y.value={},_.value="",L.value="",M.value=!1,s.value="",n.value="",o.value="",i.value={},S.value=[]}function N(e){["car","walk","bus","riding"].includes(e)&&(P.value=e,g.value=e!=="bus")}function b(e){if(!e)return"";let t="";if(e.district){const a=e.district.split(/[市区县]/);a.length>0&&a[0]?t=a[0]+"市":t=e.district}else if(e.formatted_address){const a=e.formatted_address.split(/[市区县]/);a.length>0&&a[0]&&(t=a[0]+"市")}else e.name&&(t=e.name);return t}return{mapState:g,markers:l,polyline:h,includePoints:c,latitude:d,longitude:v,city:k,latitude_e:s,longitude_e:n,city_e:o,mapEndObj:i,textData:y,distance:_,cost:L,daohang:M,gaode_type:P,transits:S,currentLocation:f,destinationLocation:m,hasValidRoute:$,setCurrentLocation:C,setDestination:T,setRouteMarkers:q,showMarkerInfo:A,changeMarkerColor:w,resetMapState:E,changeNavigationType:N}}exports.useMapState=j;
