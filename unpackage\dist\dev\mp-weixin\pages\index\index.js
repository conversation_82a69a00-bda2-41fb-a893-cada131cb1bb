"use strict";const t=require("../../common/vendor.js"),me=require("../../composables/useMapState.js"),_e=require("../../composables/useRouteService.js"),we=require("../../composables/useLocationService.js"),ke=require("../../composables/useRealTimeLocation.js"),Pe=require("../../utils/mapUtils.js"),Me=require("../../libs/amap-wx.130.js"),Se=require("../../libs/config.js");Math||(Le+Te)();const Le=()=>"../../components/Header/Header.js",Te=()=>"../../components/InputTip/InputTip.js",Ce={__name:"index",setup(Oe){const{mapState:w,markers:r,polyline:M,includePoints:I,latitude:d,longitude:f,city:c,latitude_e:p,longitude_e:h,city_e:g,mapEndObj:P,textData:S,distance:L,cost:T,daohang:m,gaode_type:v,transits:b,currentLocation:q,destinationLocation:Y,hasValidRoute:C,setCurrentLocation:N,setDestination:B,setRouteMarkers:K,showMarkerInfo:G,changeMarkerColor:H,resetMapState:V,changeNavigationType:D}=me.useMapState(),{planRoute:$}=_e.useRouteService(),{getPoiAround:O}=we.useLocationService(),{isTracking:X,currentPosition:U,hasValidPosition:J,startTracking:Q}=ke.useRealTimeLocation(),Z=t.computed(()=>{const e=!!(f.value&&d.value),n=!!(h.value&&p.value);return!(e&&n)}),A=t.computed(()=>{if(!c.value||!g.value)return!1;const e=a=>a?a.replace(/[市区县]/g,"").trim():"",n=e(c.value),i=e(g.value);return n&&i&&n!==i});let s=[];const k=t.ref(-1);t.onLoad(async()=>{ie();try{await Q(),console.log("✅ 实时定位已自动开启")}catch(e){console.warn("⚠️ 实时定位开启失败:",e)}}),t.watchEffect(()=>{if(X.value&&J.value){const e=U.value;m.value||(f.value=e.longitude,d.value=e.latitude,ee(e))}});function ee(e){if(!s||s.length===0)return;const n=s.findIndex(i=>i.id===999);n!==-1&&(s[n]={...s[n],latitude:e.latitude,longitude:e.longitude},r.value=[...s])}function te(e){const n=e.markerId;(s==null?void 0:s.length)>0&&(G(s,n),H(s,n))}function ne(e){D(e.gaode_type),C.value&&R()}function F(e){const{info:n,inputType:i}=e;if(n===null){i==="start"?se():i==="end"&&ce();return}i==="start"?oe(n):i==="end"&&le(n)}function ie(){O({iconPath:"/static/marker.png",iconPathSelected:"/static/marker_checked.png"},e=>{var i,a;if(!((i=e==null?void 0:e.markers)!=null&&i.length)){t.index.showToast({title:"无法获取当前位置",icon:"none",duration:3e3});return}const n=e.markers.map((u,o)=>({id:u.id??o,latitude:parseFloat(u.latitude),longitude:parseFloat(u.longitude),iconPath:u.iconPath||"/static/marker.png",width:u.width||23,height:u.height||33,name:u.name||`POI-${o}`,address:u.address||"未知地址",callout:{content:u.name||`POI-${o}`,display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5,textAlign:"center"}}));if(s=n,(a=e.currentLocation)!=null&&a.location){const u=e.currentLocation.location.split(",");if(u.length===2){f.value=parseFloat(u[0]),d.value=parseFloat(u[1]),c.value=e.currentLocation.name||"";const o={id:999,latitude:parseFloat(u[1]),longitude:parseFloat(u[0]),iconPath:"/static/marker_checked.png",width:23,height:33,name:e.currentLocation.name||"当前位置",address:e.currentLocation.district||e.currentLocation.address||"",callout:{content:e.currentLocation.name||"当前位置",display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8,textAlign:"center"}};r.value=[o,...n]}}else{r.value=n;const u=n[0];u&&(f.value=u.longitude,d.value=u.latitude,c.value=u.name)}I.value=r.value.filter(u=>(u==null?void 0:u.latitude)&&(u==null?void 0:u.longitude)).map(u=>({latitude:u.latitude,longitude:u.longitude}))},()=>{ae()})}function ae(){t.index.getLocation({type:"gcj02",success:e=>{f.value=e.longitude,d.value=e.latitude,c.value="当前位置";const n={iconPath:"/static/marker_checked.png",id:999,latitude:e.latitude,longitude:e.longitude,width:23,height:33,name:"当前位置",address:"设备定位",callout:{content:"当前位置",display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8}};if((s==null?void 0:s.length)>0){const i=s.filter(u=>u.id!==999),a=[n,...i];s=a,r.value=a}else s=[n],r.value=[n];t.index.showToast({title:"已获取设备位置",icon:"success",duration:2e3})},fail:()=>{ue()}})}function ue(){const e={name:"天安门",address:"北京市东城区东长安街",latitude:39.90923,longitude:116.397428};f.value=e.longitude,d.value=e.latitude,c.value=e.name;const a=[{iconPath:"/static/marker_checked.png",id:999,latitude:e.latitude,longitude:e.longitude,width:23,height:33,name:e.name,address:e.address,callout:{content:e.name,display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8}},...[{iconPath:"/static/marker.png",id:1,latitude:39.91023,longitude:116.398428,width:23,height:33,name:"测试POI1",address:"测试地址1",callout:{content:"测试POI1",display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}},{iconPath:"/static/marker.png",id:2,latitude:39.90823,longitude:116.396428,width:23,height:33,name:"测试POI2",address:"测试地址2",callout:{content:"测试POI2",display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}}]];s=a,r.value=a,t.index.showToast({title:"已设置默认位置",icon:"none",duration:2e3})}function oe(e){if(!e){t.index.showToast({title:"位置信息无效",icon:"none",duration:2e3});return}const n={latitude_e:p.value,longitude_e:h.value,city_e:g.value,mapEndObj:{...P.value}};if(V(),N(e),n.latitude_e&&n.longitude_e&&(p.value=n.latitude_e,h.value=n.longitude_e,g.value=n.city_e,P.value=n.mapEndObj,m.value=!0),e.location){const i=e.location.split(",");if(i.length===2){const a={iconPath:"/static/marker_checked.png",id:0,latitude:parseFloat(i[1]),longitude:parseFloat(i[0]),width:23,height:33,name:e.name||"起点",address:e.district||e.address||""};s=[a],r.value=[a]}}C.value&&R()}function le(e){if(!e){t.index.showToast({title:"位置信息无效",icon:"none",duration:2e3});return}B(e),C.value&&R()}function se(){const e={latitude_e:p.value,longitude_e:h.value,city_e:g.value,mapEndObj:{...P.value}};f.value="",d.value="",c.value="",m.value=!1,r.value=[],M.value=[],I.value=[],S.value={},L.value="",T.value="",w.value=!0,b.value=[],e.latitude_e&&e.longitude_e&&(p.value=e.latitude_e,h.value=e.longitude_e,g.value=e.city_e,P.value=e.mapEndObj),setTimeout(()=>{!f.value&&!d.value&&O({iconPath:"/static/marker.png",iconPathSelected:"/static/marker_checked.png"},n=>{var i;if(((i=n==null?void 0:n.markers)==null?void 0:i.length)>0){const u=Pe.safeFilterMarkers(n.markers).map((o,l)=>({id:o.id??l,latitude:parseFloat(o.latitude),longitude:parseFloat(o.longitude),iconPath:"/static/marker.png",width:23,height:33,name:o.name||`POI-${l}`,address:o.address||"未知地址",callout:{content:o.name||`POI-${l}`,display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}}));s=u,r.value=u}},()=>{r.value=[],s=[]})},100)}function ce(){const e={latitude:d.value,longitude:f.value,city:c.value};h.value="",p.value="",g.value="",P.value={},m.value=!1,M.value=[],L.value="",T.value="",b.value=[],S.value={},e.latitude&&e.longitude&&(d.value=e.latitude,f.value=e.longitude,c.value=e.city,setTimeout(()=>{d.value&&f.value&&O({iconPath:"/static/marker.png",iconPathSelected:"/static/marker_checked.png"},n=>{var i;if(((i=n==null?void 0:n.markers)==null?void 0:i.length)>0){const a=n.markers.filter(l=>l&&typeof l.latitude=="number"&&typeof l.longitude=="number"&&!isNaN(l.latitude)&&!isNaN(l.longitude)&&!(l.latitude===0&&l.longitude===0)).map((l,y)=>({id:l.id??y,latitude:parseFloat(l.latitude),longitude:parseFloat(l.longitude),iconPath:"/static/marker.png",width:23,height:33,name:l.name||`POI-${y}`,address:l.address||"未知地址",callout:{content:l.name||`POI-${y}`,display:"BYCLICK",fontSize:12,borderRadius:5,bgColor:"#ffffff",padding:5}})),o=[{id:999,latitude:e.latitude,longitude:e.longitude,iconPath:"/static/marker_checked.png",width:23,height:33,name:e.city||"当前位置",address:"起点位置",callout:{content:e.city||"当前位置",display:"ALWAYS",fontSize:14,borderRadius:5,bgColor:"#ff0000",color:"#ffffff",padding:8}},...a];s=o,r.value=o}},()=>{const n={id:999,latitude:e.latitude,longitude:e.longitude,iconPath:"/static/marker_checked.png",width:23,height:33,name:e.city||"当前位置",address:"起点位置"};r.value=[n],s=[n]})},100))}function R(){if(!C.value)return;const e={origin:q.value,destination:Y.value,city:c.value||"广州",cityd:g.value};K(),$(v.value,e,re,de)}function re(e){v.value==="bus"?e.transits&&(b.value=e.transits,w.value=!1):(e.polyline&&(M.value=e.polyline),e.distance&&(L.value=e.distance),e.cost&&(T.value=e.cost),w.value=!0)}function de(e){console.log("🔍 错误处理 - 收到的error对象:",e);let n="路线规划失败，请重试";if(e!=null&&e.message){if(n=e.message,console.log("🔍 检查showWalkingSuggestion:",e.showWalkingSuggestion),e.showWalkingSuggestion){const i=e.walkingTime||0,a=e.transitTime||0;console.log("🚀 准备显示弹窗:",{walkingTime:i,transitTime:a}),t.index.showToast({title:`步行更便利！步行${i}分钟，公交${a}分钟`,icon:"none",duration:3e3}),setTimeout(()=>{t.index.showModal({title:"出行建议",content:`步行更加便利！

步行时间：约${i}分钟
公交时间：约${a}分钟

建议您选择步行出行`,showCancel:!0,cancelText:"继续查看公交",confirmText:"切换步行",success:u=>{u.confirm?(v.value="walk",$()):u.cancel&&fe()}})},500);return}}else v.value==="bus"&&(c.value&&g.value&&c.value!==g.value?n=`暂无从${c.value}到${g.value}的公交路线，建议选择驾车或火车出行`:n="未找到合适的公交路线，请尝试其他出行方式");t.index.showToast({title:n,icon:"none",duration:3e3})}function fe(){const e={origin:`${f.value},${d.value}`,destination:`${h.value},${p.value}`,city:c.value,cityd:g.value},n=new Me.AMapWX.AMapWX({key:Se.gaode_key.key}),i={origin:e.origin,destination:e.destination,city:e.city};e.cityd&&e.cityd!==e.city&&Object.assign(i,{cityd:e.cityd}),n.getTransitRoute({...i,success:function(a){a!=null&&a.transits?(b.value=a.transits,w.value=!1):t.index.showToast({title:"未找到公交路线",icon:"none",duration:2e3})},fail:function(a){t.index.showToast({title:"公交路线获取失败",icon:"none",duration:2e3})}})}function ge(e){const n=new Date,i=new Date(n.getTime()+e*1e3),a=i.getHours().toString().padStart(2,"0"),u=i.getMinutes().toString().padStart(2,"0");return`${a}:${u}`}function ve(e){var i;const n=[];return(i=e.segments)!=null&&i.length&&e.segments.forEach(a=>{var u,o,l;if((u=a.walking)!=null&&u.distance&&parseInt(a.walking.distance)>0&&n.push({type:"walking",distance:a.walking.distance}),(l=(o=a.bus)==null?void 0:o.buslines)!=null&&l[0]){const y=a.bus.buslines[0];let _=y.name;_.includes("(")&&(_=_.split("(")[0]),n.push({type:"bus",busline:_,stationCount:y.via_num||0})}}),n}function pe(e){k.value=k.value===e?-1:e}function he(e,n){t.index.showToast({title:`已选择方案${n+1}`,icon:"success",duration:1500})}return(e,n)=>t.e({a:t.o(ne),b:t.p({type:t.unref(v),NavigationOrNot:t.unref(m),buttonsDisabled:t.unref(Z)}),c:t.o(F),d:t.p({city:t.unref(c),longitude:t.unref(f),latitude:t.unref(d),defaultValue:"当前位置",inputType:"start"}),e:t.o(F),f:t.p({city:t.unref(g),longitude:t.unref(h),latitude:t.unref(p),defaultValue:"目的地",inputType:"end"}),g:t.unref(v)!=="bus"&&t.unref(w)},t.unref(v)!=="bus"&&t.unref(w)?{h:t.unref(f),i:t.unref(d),j:t.unref(r),k:t.o(te),l:t.unref(M),m:t.unref(I)}:{},{n:t.unref(v)==="bus"&&t.unref(b).length>0},t.unref(v)==="bus"&&t.unref(b).length>0?t.e({o:t.t(t.unref(b).length),p:t.unref(A)},t.unref(A)?{}:{},{q:t.f(t.unref(b),(i,a,u)=>t.e({a:t.t(Math.round(i.duration/60)),b:t.t(ge(i.duration)),c:a===0},a===0?{}:{},{d:i.nightflag==="1"},i.nightflag==="1"?{}:{},{e:t.f(ve(i),(o,l,y)=>t.e({a:l>0},l>0?{}:{},{b:o.type==="walking"},o.type==="walking"?{c:t.t(o.distance)}:o.type==="bus"?{e:t.t(o.busline),f:t.t(o.stationCount)}:{},{d:o.type==="bus",g:l})),f:t.t((i.distance/1e3).toFixed(1)),g:t.t(i.cost),h:t.t(Math.round(i.walking_distance)),i:t.t(k.value===a?"收起详情":"查看详情"),j:t.t(k.value===a?"▲":"▼"),k:t.o(o=>pe(a),a),l:k.value===a},k.value===a?{m:t.f(i.segments,(o,l,y)=>{var _,E,j,z,x,W;return t.e({a:(_=o.walking)==null?void 0:_.distance},(E=o.walking)!=null&&E.distance?{b:t.t(o.walking.distance),c:t.t(Math.round(o.walking.duration/60)),d:t.f(o.walking.steps,(be,ye,Re)=>({a:t.t(be.instruction),b:ye}))}:{},{e:(z=(j=o.bus)==null?void 0:j.buslines)==null?void 0:z[0]},(W=(x=o.bus)==null?void 0:x.buslines)!=null&&W[0]?{f:t.t(o.bus.buslines[0].name),g:t.t(Math.round(o.bus.buslines[0].duration/60)),h:t.t(o.bus.buslines[0].departure_stop.name),i:t.t(o.bus.buslines[0].arrival_stop.name),j:t.t(o.bus.buslines[0].start_time),k:t.t(o.bus.buslines[0].end_time)}:{},{l})})}:{},{n:a,o:t.o(o=>he(i,a),a)}))}):{},{r:t.unref(v)!=="bus"},t.unref(v)!=="bus"?t.e({s:t.t(t.unref(S).name),t:t.t(t.unref(S).desc),v:t.unref(m)},t.unref(m)?{w:t.t(t.unref(L)),x:t.t(t.unref(T))}:{}):{})}},Ie=t._export_sfc(Ce,[["__file","D:/zuomian/前端学习/uniapp项目/GaodeMap/pages/index/index.vue"]]);wx.createPage(Ie);
